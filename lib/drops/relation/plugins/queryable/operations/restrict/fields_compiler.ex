defmodule Drops.Relation.Plugins.Queryable.Operations.Restrict.FieldsCompiler do
  @moduledoc """
  Compiler for generating sophisticated where clauses from field-based restrictions.

  This module analyzes relation schema fields and generates appropriate Ecto.Query
  where clauses based on value types:

  - Lists use `in` expressions
  - Booleans use direct equality comparison
  - Nil values use `is_nil` expressions
  - Other values use equality comparison

  Only fields that exist in the relation schema are processed, invalid field names
  are ignored to prevent runtime errors.

  ## Examples

      # Simple equality
      opts = [name: "<PERSON>", active: true]
      query = FieldsCompiler.build_where_clauses(base_query, opts, schema)

      # List values (generates IN clause)
      opts = [status: [:active, :pending]]
      query = FieldsCompiler.build_where_clauses(base_query, opts, schema)

      # Nil values
      opts = [deleted_at: nil]
      query = FieldsCompiler.build_where_clauses(base_query, opts, schema)
  """

  import Ecto.Query
  alias Drops.Relation.Schema

  @doc """
  Builds where clauses for the given query based on field restrictions.

  Analyzes each field-value pair in opts and generates appropriate where clauses
  based on the value type. Only processes fields that exist in the schema.

  ## Parameters

  - `queryable` - The base Ecto query to add where clauses to
  - `opts` - Keyword list of field-value pairs for restrictions
  - `schema` - The Drops.Relation.Schema containing field definitions

  ## Returns

  Returns the query with appropriate where clauses added.

  ## Examples

      schema = %Drops.Relation.Schema{fields: [...]}
      opts = [name: "John", active: true, tags: ["admin", "user"]]
      query = FieldsCompiler.build_where_clauses(base_query, opts, schema)
  """
  @spec build_where_clauses(Ecto.Queryable.t(), keyword(), Schema.t()) :: Ecto.Query.t()
  def build_where_clauses(queryable, opts, schema) when is_list(opts) do
    valid_field_opts = filter_valid_fields(opts, schema)

    Enum.reduce(valid_field_opts, queryable, fn {field, value}, query ->
      build_where_clause(query, field, value)
    end)
  end

  @doc """
  Filters opts to only include fields that exist in the schema.

  This prevents runtime errors from invalid field names and ensures
  only valid database columns are used in where clauses.

  ## Parameters

  - `opts` - Keyword list of field-value pairs
  - `schema` - The Drops.Relation.Schema containing field definitions

  ## Returns

  Returns a filtered keyword list containing only valid fields.

  ## Examples

      opts = [name: "John", invalid_field: "value", active: true]
      valid_opts = FieldsCompiler.filter_valid_fields(opts, schema)
      # Returns: [name: "John", active: true] (assuming invalid_field doesn't exist)
  """
  @spec filter_valid_fields(keyword(), Schema.t()) :: keyword()
  def filter_valid_fields(opts, schema) when is_list(opts) do
    field_names = get_field_names(schema)

    Enum.filter(opts, fn {field, _value} ->
      field in field_names
    end)
  end

  # Private functions

  defp get_field_names(%Schema{fields: fields}) do
    Enum.map(fields, & &1.name)
  end

  defp build_where_clause(query, field, value) when is_list(value) do
    # Use IN clause for list values
    where(query, [r], field(r, ^field) in ^value)
  end

  defp build_where_clause(query, field, nil) do
    # Use is_nil for nil values
    where(query, [r], is_nil(field(r, ^field)))
  end

  defp build_where_clause(query, field, value) when is_boolean(value) do
    # Direct equality for boolean values
    where(query, [r], field(r, ^field) == ^value)
  end

  defp build_where_clause(query, field, value) do
    # Default equality comparison for other types
    where(query, [r], field(r, ^field) == ^value)
  end
end
