defmodule Drops.Relation.Plugins.Queryable.Operations.Restrict.FieldsCompiler do
  alias Drops.Relation.Schema

  import Ecto.Query

  @spec visit(Ecto.Queryable.t(), map()) :: Ecto.Query.t()
  def visit(queryable, %{opts: opts, schema: schema}) when is_list(opts) do
    valid_field_opts = visit(opts, %{schema: schema, filter: :valid_fields})

    Enum.reduce(valid_field_opts, queryable, fn {field, value}, query ->
      visit({field, value}, %{query: query})
    end)
  end

  def visit(opts, %{schema: schema, filter: :valid_fields}) when is_list(opts) do
    field_names = visit(schema, %{extract: :field_names})

    Enum.filter(opts, fn {field, _value} ->
      field in field_names
    end)
  end

  def visit(%Schema{fields: fields}, %{extract: :field_names}) do
    Enum.map(fields, &visit(&1, %{extract: :name}))
  end

  def visit(%{name: name}, %{extract: :name}), do: name

  def visit({field, value}, %{query: query}) when is_list(value) do
    where(query, [r], field(r, ^field) in ^value)
  end

  def visit({field, nil}, %{query: query}) do
    where(query, [r], is_nil(field(r, ^field)))
  end

  def visit({field, value}, %{query: query}) when is_boolean(value) do
    where(query, [r], field(r, ^field) == ^value)
  end

  def visit({field, value}, %{query: query}) do
    where(query, [r], field(r, ^field) == ^value)
  end
end
