defmodule Drops.Relation.Plugins.Queryable.Operations.Order.FieldsCompilerTest do
  use ExUnit.Case, async: true

  alias Drops.Relation.Plugins.Queryable.Operations.Order.FieldsCompiler
  alias Drops.Relation.Schema
  alias Drops.Relation.Schema.Field

  import Ecto.Query

  describe "visit/2 with queryable and opts" do
    test "applies order by for valid single field" do
      # Create a simple schema with name and email fields
      name_field = Field.new(:name, :string, %{source: :name})
      email_field = Field.new(:email, :string, %{source: :email})
      schema = Schema.new(:users, nil, [], [name_field, email_field], [])

      # Create a base query
      base_query = from(u in "users")

      # Apply order by name
      opts = [order: :name]
      result_query = FieldsCompiler.visit(base_query, %{opts: opts, schema: schema})

      # Verify the query has the order_by clause
      assert %Ecto.Query{order_bys: [_]} = result_query
      assert length(result_query.order_bys) == 1
    end

    test "applies order by for valid multiple fields" do
      name_field = Field.new(:name, :string, %{source: :name})
      email_field = Field.new(:email, :string, %{source: :email})
      schema = Schema.new(:users, nil, [], [name_field, email_field], [])

      base_query = from(u in "users")

      # Apply order by multiple fields
      opts = [order: [:name, :email]]
      result_query = FieldsCompiler.visit(base_query, %{opts: opts, schema: schema})

      # Verify the query has multiple order_by clauses
      assert %Ecto.Query{order_bys: order_bys} = result_query
      assert length(order_bys) == 2
    end

    test "applies order by with direction specifications" do
      name_field = Field.new(:name, :string, %{source: :name})
      email_field = Field.new(:email, :string, %{source: :email})
      schema = Schema.new(:users, nil, [], [name_field, email_field], [])

      base_query = from(u in "users")

      # Apply order by with directions
      opts = [order: [{:desc, :name}, {:asc, :email}]]
      result_query = FieldsCompiler.visit(base_query, %{opts: opts, schema: schema})

      # Verify the query has the correct order_by clauses with directions
      assert %Ecto.Query{order_bys: order_bys} = result_query
      assert length(order_bys) == 2
    end

    test "ignores invalid field names" do
      name_field = Field.new(:name, :string, %{source: :name})
      schema = Schema.new(:users, nil, [], [name_field], [])

      base_query = from(u in "users")

      # Try to order by invalid field
      opts = [order: :invalid_field]
      result_query = FieldsCompiler.visit(base_query, %{opts: opts, schema: schema})

      # Query should remain unchanged
      assert result_query == base_query
    end

    test "filters out invalid fields from list" do
      name_field = Field.new(:name, :string, %{source: :name})
      schema = Schema.new(:users, nil, [], [name_field], [])

      base_query = from(u in "users")

      # Mix valid and invalid fields
      opts = [order: [:name, :invalid_field, :another_invalid]]
      result_query = FieldsCompiler.visit(base_query, %{opts: opts, schema: schema})

      # Should only apply order for valid field
      assert %Ecto.Query{order_bys: [_]} = result_query
    end

    test "handles empty opts gracefully" do
      name_field = Field.new(:name, :string, %{source: :name})
      schema = Schema.new(:users, nil, [], [name_field], [])

      base_query = from(u in "users")

      # No order option
      opts = []
      result_query = FieldsCompiler.visit(base_query, %{opts: opts, schema: schema})

      # Query should remain unchanged
      assert result_query == base_query
    end

    test "handles nil order value" do
      name_field = Field.new(:name, :string, %{source: :name})
      schema = Schema.new(:users, nil, [], [name_field], [])

      base_query = from(u in "users")

      # Nil order option
      opts = [order: nil]
      result_query = FieldsCompiler.visit(base_query, %{opts: opts, schema: schema})

      # Query should remain unchanged
      assert result_query == base_query
    end
  end

  describe "visit/2 with field validation" do
    test "extracts field names from schema correctly" do
      name_field = Field.new(:name, :string, %{source: :name})
      email_field = Field.new(:email, :string, %{source: :email})
      age_field = Field.new(:age, :integer, %{source: :age})
      schema = Schema.new(:users, nil, [], [name_field, email_field, age_field], [])

      field_names = FieldsCompiler.visit(schema, %{extract: :field_names})

      assert field_names == [:name, :email, :age]
    end

    test "validates single field correctly" do
      name_field = Field.new(:name, :string, %{source: :name})
      email_field = Field.new(:email, :string, %{source: :email})
      schema = Schema.new(:users, nil, [], [name_field, email_field], [])

      # Valid field
      result = FieldsCompiler.visit(:name, %{schema: schema, filter: :valid_fields})
      assert result == :name

      # Invalid field
      result = FieldsCompiler.visit(:invalid, %{schema: schema, filter: :valid_fields})
      assert result == nil
    end

    test "validates field list correctly" do
      name_field = Field.new(:name, :string, %{source: :name})
      email_field = Field.new(:email, :string, %{source: :email})
      schema = Schema.new(:users, nil, [], [name_field, email_field], [])

      # Mix of valid and invalid fields
      fields = [:name, :invalid, :email, :another_invalid]
      result = FieldsCompiler.visit(fields, %{schema: schema, filter: :valid_fields})
      
      assert result == [:name, :email]
    end

    test "validates direction tuples correctly" do
      name_field = Field.new(:name, :string, %{source: :name})
      email_field = Field.new(:email, :string, %{source: :email})
      schema = Schema.new(:users, nil, [], [name_field, email_field], [])

      # Valid direction tuples
      fields = [{:asc, :name}, {:desc, :email}, {:invalid_direction, :name}, {:asc, :invalid_field}]
      result = FieldsCompiler.visit(fields, %{schema: schema, filter: :valid_fields})
      
      assert result == [{:asc, :name}, {:desc, :email}]
    end
  end
end
