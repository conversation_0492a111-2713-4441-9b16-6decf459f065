defmodule Drops.Relations.Plugins.WritingTest do
  use Drops.RelationCase, async: false

  describe "changeset/2 function" do
    @tag relations: [:users]
    test "creates changeset from map", %{users: users} do
      params = %{name: "<PERSON>", email: "<EMAIL>", age: 30}
      changeset = users.changeset(params)

      assert changeset.__struct__ == Ecto.Changeset
      assert changeset.valid?
      assert changeset.changes.name == "John"
      assert changeset.changes.email == "<EMAIL>"
      assert changeset.changes.age == 30
    end

    @tag relations: [:users]
    test "creates changeset from struct with changes", %{users: users} do
      # First create a user
      {:ok, user} = users.insert(%{name: "<PERSON>", email: "<EMAIL>"})

      # Then create changeset with changes
      changeset = users.changeset(user, %{name: "<PERSON>"})

      assert changeset.__struct__ == Ecto.Changeset
      assert changeset.valid?
      assert changeset.changes.name == "Jane Updated"
      assert changeset.data.id == user.id
    end

    @tag relations: [:users]
    test "changeset can be used with insert", %{users: users} do
      changeset = users.changeset(%{name: "<PERSON>", email: "<EMAIL>"})
      {:ok, user} = users.insert(changeset)

      assert user.name == "<PERSON>"
      assert user.email == "<EMAIL>"
    end
  end

  describe "update functions" do
    @tag relations: [:users]
    test "update accepts changeset", %{users: users} do
      {:ok, user} = users.insert(%{name: "Alice", email: "<EMAIL>"})

      changeset = users.changeset(user, %{name: "Alice Updated"})
      {:ok, updated_user} = users.update(changeset)

      assert updated_user.name == "Alice Updated"
      assert updated_user.id == user.id
    end

    @tag relations: [:users]
    test "update accepts struct and attributes", %{users: users} do
      {:ok, user} = users.insert(%{name: "Charlie", email: "<EMAIL>"})

      # Update with struct and attributes
      {:ok, updated_user} = users.update(user, %{name: "Charlie Updated"})

      assert updated_user.name == "Charlie Updated"
      assert updated_user.id == user.id
    end
  end

  describe "update! functions" do
    @tag relations: [:users]
    test "update! accepts struct and attributes", %{users: users} do
      {:ok, user} = users.insert(%{name: "David", email: "<EMAIL>"})

      # Update with struct and attributes
      updated_user = users.update!(user, %{name: "David Updated"})

      assert updated_user.name == "David Updated"
      assert updated_user.id == user.id
    end

    @tag relations: [:users]
    test "update! accepts changeset", %{users: users} do
      {:ok, user} = users.insert(%{name: "Eve", email: "<EMAIL>"})

      changeset = users.changeset(user, %{name: "Eve Updated"})
      updated_user = users.update!(changeset)

      assert updated_user.name == "Eve Updated"
      assert updated_user.id == user.id
    end
  end
end
