defmodule Drops.Relation.ViewTest do
  use Drops.RelationCase, async: false

  describe "defining a relation view" do
    relation(:users) do
      schema("users", infer: true)

      view(:active) do
        schema([:id, :name, :active])

        derive do
          restrict(active: true)
        end
      end
    end

    test "returns relation view", %{users: users} do
      users.insert(%{name: "<PERSON>", active: false})
      users.insert(%{name: "<PERSON>", active: true})
      users.insert(%{name: "<PERSON>", active: false})
      users.insert(%{name: "<PERSON>", active: true})

      [jane, jade] = users.active().all()

      assert jane.name == "<PERSON>"
      assert jane.active
      assert :email not in Map.keys(jane)

      assert jade.name == "Jade"
      assert jade.active
      assert :email not in Map.keys(jade)
    end
  end

  describe "defining a relation view with custom struct name" do
    relation(:users) do
      schema("users", infer: true)

      view(:active) do
        schema([:id, :name, :active], struct: "<PERSON>User")

        derive do
          restrict(active: true)
        end
      end
    end

    test "returns relation view", %{users: users} do
      users.insert(%{name: "<PERSON>", active: false})
      users.insert(%{name: "<PERSON>", active: true})
      users.insert(%{name: "<PERSON>", active: false})
      users.insert(%{name: "Jade", active: true})

      [jane, jade] = users.view(:active).all()

      assert jane.__struct__ == Test.Relations.Users.Active.ActiveUser
      assert jane.name == "Jane"
      assert jane.active
      assert :email not in Map.keys(jane)

      assert jade.__struct__ == Test.Relations.Users.Active.ActiveUser
      assert jade.name == "Jade"
      assert jade.active
      assert :email not in Map.keys(jade)
    end
  end
end
