defmodule Users do
  use Drops.Relation, repo: Drops.Relation.Repos.Postgres

  schema("users", infer: true)

  view(:active) do
    schema([:id, :name, :active])

    derive do
      restrict(active: true)
    end
  end
end

Enum.each(Users.all(), &Users.delete/1)

Users.insert(%{name: "<PERSON>", active: false})
Users.insert(%{name: "<PERSON>", active: true})
Users.insert(%{name: "<PERSON>", active: false})
Users.insert(%{name: "<PERSON>", active: true})

IO.inspect(Users.last())
# %Users.Schemas.User{
#   __meta__: #Ecto.Schema.Metadata<:loaded, "users">,
#   id: 4,
#   name: "<PERSON>",
#   email: nil,
#   age: nil,
#   active: true,
#   inserted_at: ~N[2025-07-16 21:56:47],
#   updated_at: ~N[2025-07-16 21:56:47]
# }

IO.inspect(Users.active() |> Enum.to_list())
# [
#   %Users.Schemas.Active{
#     __meta__: #Ecto.Schema.Metadata<:loaded, "users">,
#     id: 2,
#     name: "<PERSON>",
#     active: true
#   },
#   %Users.Schemas.Active{
#     __meta__: #Ecto.Schema.Metadata<:loaded, "users">,
#     id: 4,
#     name: "Jade",
#     active: true
#   }
# ]
